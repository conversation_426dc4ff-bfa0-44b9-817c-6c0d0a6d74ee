Goal: build a complete, responsive, multi-page web app bookstore with Buyer / Seller / Admin roles, full CRUD for books, checkout, localStorage persistence (including images saved as base64), toast notifications for each of 15 criteria, and a final “all done” toast. No backend required. Tech: Next.js + Tailwind CSS (or plain CSS if you prefer), Chart.js for admin charts, Toastify (or a light toast lib) for toasts, uuid for ids. Keep UI theme mostly white + a light accent color (suggested accent: #E6F0FF — light pastel blue). Deliverables: runnable project, README with how to run, and a small exported PNG flowchart image of the app flow.

Requirements / Acceptance Criteria (behavioural)

All data (users, books, carts, orders) must be saved to localStorage. Images uploaded by sellers must be converted to base64 and saved to localStorage with the book record.

On first use of a functionality that satisfies each of the 15 criteria, show a toast:
✅ Criteria X – <short name> – Complete (e.g. ✅ Criteria 1 – Arrays – Complete).
When all 15 have been triggered at least once, show a grand toast: 🎉 All 15 Criteria Completed Successfully.

Default admin credentials: <EMAIL> / Admin@123 (explain in README).

App must be responsive (mobile + desktop).

Basic UI / Pages (SPA view states)

Auth Page (Register / Login)

Registration asks: name, email, password, role (Buyer or Seller) — store role in user object.

Login verifies against localStorage.

After login, set a session flag in memory and localStorage: currentUser and isLoggedIn.

Main Dashboard – depends on role:

Buyer View: Book Showcase, Search, Sort, Add to Cart, Cart, Checkout, Profile (purchased books)

Seller View: Add Book form, My Books list (edit/delete own books), Upload image (converted to base64)

Admin View: Admin panel showing All Users, All Books (from everyone), All Orders; Admin can update book info or delete books; block/unblock users; show sales chart.

Global UI:

Header with user name + role + logout

Toast container (Toastify)

Progress/Checklist UI (optional) showing which criteria toasts triggered (helps testing)

Data Models (store in localStorage keys)

Use these shapes as JS objects (examples):

User:

{
  id: "uuid",
  name: "Mihir",
  email: "<EMAIL>",
  passwordHash: "bcrypt-simulated-or-plain", // simple for demo
  role: "buyer" | "seller" | "admin",
  blocked: false
}


Book:

{
  id: "uuid",
  title: "Book Title",
  author: "Author Name",
  price: 499.00,
  stock: 12,
  sellerId: "uuid-of-seller",
  imageBase64: "data:image/png;base64,...",
  createdAt: 1690000000000
}


Order:

{
  id: "uuid",
  buyerId: "uuid",
  items: [{ bookId, qty, priceAtPurchase }],
  total: 1498.00,
  createdAt: 1690000000000
}


AppState in localStorage keys:

users → array of User objects

books → array of Book objects

orders → array of Order objects

criteriaFlags → object mapping criterion1..15 to true/false (used to track which toast triggered)

currentUser → the logged in user id (or null)

Key Functions / Methods (implement these; signatures must be used)

Implement these functions as reusable modules (with parameters & return values):

registerUser({name, email, password, role}) : User → creates user object, saves to users, returns the user.

triggers Criteria 3 (Objects as data records) and Criteria 13 (File I/O via localStorage).

loginUser(email, password) : User | null → verifies credentials, sets currentUser, returns user or null.

addBook(bookData, sellerId) : Book → create Book object, push into books, returns created book. (Criteria 2)

updateBook(bookId, updates) : Book → find and update book, return updated book. (Admin & seller use; Criteria 8)

deleteBook(bookId) : boolean → removes book, returns true if removed. (Criteria 8)

getAllBooks() : Book[] → returns array (Criteria 1, 6)

searchBooksByTitle(title) : Book[] → returns array matching titles (Criteria 9 & 12)

sortBooks(by: 'price'|'title', order: 'asc'|'desc') : Book[] → returns sorted array (Criteria 11)

addToCart(userId, bookId, qty) : Cart → update a per-session cart object (in memory and optionally localStorage)

calculateTotal(cart) : number → returns total (Criteria 10)

checkout(userId, cart) : Order → complex selection: check stock, update book stock, create order, return Order object (Criteria 5, 7)

convertImageFileToBase64(file) : Promise<string> → used to store images in localStorage.

markCriterionComplete(n: number, taskName: string) : void → set criteriaFlags and show toast if not already set. When all flags true, show final toast.

Where Each of the 15 Criteria Are Demonstrated (explicit checklist)

Include these explicit triggers in the code (call markCriterionComplete where noted):

Arrays — getAllBooks() and books array. (Trigger when books are loaded on first view.)

User-defined objects — addBook() creates Book object. (Trigger on first new book created.)

Objects as data records — registerUser() stores User object. (Trigger on first registration.)

Simple selection (if-else) — Role selection at registration/login (if (role==='buyer') ... else ...). (Trigger when user picks role.)

Complex selection — checkout() checks: if (user.role==='buyer') { if (stock>=qty && balanceOk) { … } else if (...) } (Trigger when a checkout attempt is made.)

Loops — Rendering the books list (books.forEach(...) or for loop). (Trigger when book list renders.)

Nested loops — Admin view: iterating users then each user’s orders (outer loop users, inner loop orders). (Trigger when admin views users & orders.)

User-defined methods — addBook(), updateBook(), deleteBook() (Trigger when any of these methods are invoked.)

User-defined methods with parameters — searchBooksByTitle(title) (Trigger when search performed with a parameter.)

User-defined methods with return values — calculateTotal(cart) returns number. (Trigger when cart total is displayed/used.)

Sorting — sortBooks('price','asc') (Trigger when user sorts.)

Searching — searchBooksByTitle or filter by author (Trigger when search used.)

File I/O — localStorage.setItem/getItem for users, books, orders and saving image base64. (Trigger when initial save/load occurs.)

Use of additional libraries — Chart.js rendering sales graph in Admin. (Trigger when chart mounts.)

Use of sentinels or flags — isLoggedIn, isAdmin, and criteriaFlags sentinel object. (Trigger at login & when criteria flags used.)

UI & UX Details

Theme: background #FFFFFF, accent #E6F0FF for buttons, cards, subtle shadows. Use rounded corners and airy spacing.

Forms: validation messages; file input preview; image preview before upload.

Toasts: use Toastify or similar. Style success toast with check icon.

Checklist UI (optional): small grid of 15 chips that turns green when each criterion is complete — useful for demo to client.

Chart: Admin sales chart that shows total sales per day/month using Chart.js (pull data from orders array).

Accessibility: use semantic HTML, labels for inputs.

Testing / Demo Steps to Validate (include in README)

Pre-populate localStorage with sample data (2 sellers, 5 books, 2 buyers) to speed demo. Provide a script seedLocalStorage() that runs on first load if no data present.

Demo flow:

Register as Seller → add book (watch Criteria 2 & 3 toasts).

Register as Buyer → search → add to cart → checkout (watch criteria 5, 10, 11, 12).

Login Admin → view all books → update book → delete book (watch Criteria 8,14).

Show final toast after all criteria complete.

File Structure (suggested)
/src
  /components
    AuthForm.jsx
    RolePrompt.jsx
    BookCard.jsx
    BookList.jsx
    SellerDashboard.jsx
    BuyerDashboard.jsx
    AdminPanel.jsx
    Cart.jsx
    Profile.jsx
    CriteriaChecklist.jsx
  /utils
    storage.js           // wrappers for localStorage
    image.js             // file->base64 helpers
    booksService.js      // all book methods (add/update/delete/search/sort)
    userService.js
    orderService.js
    criteriaTracker.js   // markCriterionComplete
  main.jsx
  App.jsx
  index.css (Tailwind)
README.md

Extra Implementation Notes

Store images as base64 data URLs: reader.readAsDataURL(file) then save string in book.imageBase64.

IDs: use uuid for unique IDs.

Password hashing: for demo, plain text is acceptable; note in README that real apps must hash server-side.

localStorage size: warn about large images — recommend small images or throttling. For demo, use small images or compress client side.

Error handling: show toasts on success and failure.

Security: this is a demo with no backend — add README warnings.

Final Output Requested from Jules

A runnable repository Next.js with instructions to run locally (npm i && npm run dev).

Pre-seeded demo data + seedLocalStorage() function.

A short README describing the 15 criteria and where to find their implementation.

A small exported flowchart PNG of the app flow (SVG/PNG) matching the flowchart described previously.

All toasts working as specified; final grand toast when all criteria are complete.

A deployed preview link (optional) or a zipped build.