# BookStore - Educational Multi-Role Web Application

A complete, responsive bookstore application demonstrating 15 fundamental programming concepts with role-based functionality (Buyer/Seller/Admin), localStorage persistence, and comprehensive CRUD operations.

## 🚀 Quick Start

### Prerequisites
- Node.js (v18+)
- npm or yarn

### Installation & Running

```bash
# Install dependencies
npm install

# Start development server
npm run dev
