# Overview

BookStore is an educational multi-role web application that demonstrates 15 fundamental programming concepts through a complete, responsive bookstore system. The application features role-based functionality (Buyer/Seller/Admin), comprehensive CRUD operations for books, shopping cart functionality, and localStorage persistence with base64 image storage. Built as a full-stack TypeScript application with React frontend and Express backend, it serves as a practical learning tool for web development concepts.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture
The client-side application is built with React 18 and TypeScript, utilizing a modern component-based architecture with the following design patterns:

- **UI Framework**: Radix UI components with shadcn/ui styling for consistent, accessible interface elements
- **Styling**: Tailwind CSS with CSS custom properties for theming and responsive design
- **State Management**: React Context API for authentication and shopping cart state, with React Hook Form for form validation
- **Routing**: Wouter for lightweight client-side routing with role-based view switching
- **Data Fetching**: TanStack Query for server state management and caching

## Backend Architecture
The server-side follows a RESTful API design pattern with Express.js:

- **Runtime**: Node.js with TypeScript for type safety
- **API Layer**: Express.js with middleware for request logging and error handling
- **Data Validation**: Zod schemas for runtime type checking and validation
- **Storage Layer**: In-memory storage with localStorage fallback for persistence
- **Development Setup**: Vite for development server with hot module replacement

## Data Storage Solutions
The application implements a dual-storage approach:

- **Primary Storage**: localStorage for client-side persistence of users, books, orders, and criteria tracking
- **Image Storage**: Base64 encoding for image files stored directly in localStorage with book records
- **Session Management**: localStorage-based user sessions with memory-backed current user state
- **Database Schema**: Drizzle ORM configured for PostgreSQL (ready for future database integration)

## Authentication and Authorization
Role-based access control system with three distinct user roles:

- **Buyer Role**: Browse books, search/filter, add to cart, checkout, view order history
- **Seller Role**: Add/edit/delete own books, upload images, view sales analytics
- **Admin Role**: Full system access including user management, all books/orders, analytics dashboard
- **Session Handling**: localStorage-based authentication with role-specific UI rendering

## Component Design Patterns
The application follows React best practices with:

- **Compound Components**: Reusable UI components with consistent API patterns
- **Custom Hooks**: Encapsulated business logic for authentication, cart management, and mobile detection
- **Context Providers**: Centralized state management for cross-cutting concerns
- **Form Handling**: React Hook Form with Zod validation for type-safe form processing

# External Dependencies

## UI and Styling Libraries
- **@radix-ui/***: Comprehensive set of unstyled, accessible UI primitives for building the interface
- **tailwindcss**: Utility-first CSS framework for responsive design and consistent styling
- **class-variance-authority**: Utility for creating variant-based component APIs
- **lucide-react**: Icon library providing consistent iconography throughout the application

## Data Management and Validation
- **zod**: TypeScript-first schema validation for runtime type checking
- **drizzle-orm**: Type-safe ORM configured for PostgreSQL database operations
- **@tanstack/react-query**: Powerful data synchronization library for server state management

## Form and Input Handling
- **react-hook-form**: Performant forms library with minimal re-renders
- **@hookform/resolvers**: Validation resolvers for integrating with Zod schemas

## Development and Build Tools
- **vite**: Next-generation frontend build tool with fast HMR and optimized builds
- **typescript**: Static type checking for enhanced developer experience and code reliability
- **@replit/vite-plugin-***: Replit-specific plugins for development environment integration

## Charting and Visualization
- **chart.js**: Canvas-based charting library for admin analytics dashboard
- **react-chartjs-2**: React wrapper for Chart.js integration

## Database Integration
- **@neondatabase/serverless**: Serverless PostgreSQL driver for cloud database connectivity
- **drizzle-kit**: CLI tools for database migrations and schema management