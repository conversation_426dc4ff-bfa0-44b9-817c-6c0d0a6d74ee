import { useState, useEffect } from 'react';
import { Search, ShoppingCart, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BookCard } from '../components/BookCard';
import { CartSidebar } from '../components/CartSidebar';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { getAllBooks, searchBooksByTitle, sortBooks } from '../services/bookService';
import { getAllOrders } from '../services/orderService';
import { Book } from '@shared/schema';

interface BuyerDashboardProps {
  activeView?: string;
}

export function BuyerDashboard({ activeView = 'dashboard' }: BuyerDashboardProps) {
  const [books, setBooks] = useState<Book[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'title' | 'price'>('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [isCartOpen, setIsCartOpen] = useState(false);
  const { itemCount } = useCart();
  const { user } = useAuth();

  useEffect(() => {
    loadBooks();
  }, []);

  const loadBooks = () => {
    const allBooks = getAllBooks();
    setBooks(allBooks);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term.trim()) {
      const filtered = searchBooksByTitle(term);
      setBooks(filtered);
    } else {
      loadBooks();
    }
  };

  const handleSort = (by: 'title' | 'price', order: 'asc' | 'desc') => {
    setSortBy(by);
    setSortOrder(order);
    
    const currentBooks = searchTerm ? searchBooksByTitle(searchTerm) : getAllBooks();
    const sorted = sortBooks(by, order).filter(book => 
      searchTerm ? currentBooks.some(b => b.id === book.id) : true
    );
    setBooks(sorted);
  };

  const renderBooksView = () => (
    <>
      {/* Search and Filter Bar */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search books by title or author..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
              data-testid="input-book-search"
            />
          </div>
          <div className="flex gap-2">
            <Select
              value={`${sortBy}-${sortOrder}`}
              onValueChange={(value) => {
                const [by, order] = value.split('-') as ['title' | 'price', 'asc' | 'desc'];
                handleSort(by, order);
              }}
            >
              <SelectTrigger className="w-48" data-testid="select-sort">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="title-asc">Title A-Z</SelectItem>
                <SelectItem value="title-desc">Title Z-A</SelectItem>
                <SelectItem value="price-asc">Price Low-High</SelectItem>
                <SelectItem value="price-desc">Price High-Low</SelectItem>
              </SelectContent>
            </Select>
            <Button
              onClick={() => setIsCartOpen(true)}
              className="flex items-center gap-2"
              data-testid="button-open-cart"
            >
              <ShoppingCart className="h-4 w-4" />
              Cart ({itemCount})
            </Button>
          </div>
        </div>
      </div>

      {/* Book Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        {books.length === 0 ? (
          <div className="col-span-full text-center text-muted-foreground py-8" data-testid="text-no-books">
            {searchTerm ? 'No books found matching your search.' : 'No books available.'}
          </div>
        ) : (
          books.map((book) => (
            <BookCard key={book.id} book={book} />
          ))
        )}
      </div>
    </>
  );

  const renderOrdersView = () => {
    const orders = getAllOrders().filter(order => order.buyerId === user?.id);
    
    return (
      <Card>
        <CardHeader>
          <CardTitle>My Orders</CardTitle>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              You haven't placed any orders yet.
            </div>
          ) : (
            <div className="space-y-4">
              {orders.map((order) => (
                <div key={order.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">Order #{order.id.slice(0, 8)}</h4>
                      <p className="text-sm text-muted-foreground">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">${order.total.toFixed(2)}</p>
                      <Badge variant="secondary">Completed</Badge>
                    </div>
                  </div>
                  <div className="text-sm">
                    <p className="text-muted-foreground">
                      {order.items.reduce((total, item) => total + item.qty, 0)} items
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderProfileView = () => (
    <Card>
      <CardHeader>
        <CardTitle>My Profile</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-2xl font-medium">
            {user?.name.charAt(0).toUpperCase()}
          </div>
          <div>
            <h3 className="text-xl font-semibold">{user?.name}</h3>
            <p className="text-muted-foreground">{user?.email}</p>
            <Badge variant="secondary" className="mt-1">
              {user?.role.charAt(0).toUpperCase() + user?.role.slice(1)}
            </Badge>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <h4 className="font-medium">Account Information</h4>
            <div className="text-sm space-y-1">
              <p><span className="text-muted-foreground">Member since:</span> {new Date(user?.createdAt || 0).toLocaleDateString()}</p>
              <p><span className="text-muted-foreground">Account status:</span> Active</p>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">Order Statistics</h4>
            <div className="text-sm space-y-1">
              <p><span className="text-muted-foreground">Total orders:</span> {getAllOrders().filter(o => o.buyerId === user?.id).length}</p>
              <p><span className="text-muted-foreground">Total spent:</span> ${getAllOrders().filter(o => o.buyerId === user?.id).reduce((sum, order) => sum + order.total, 0).toFixed(2)}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderContent = () => {
    switch (activeView) {
      case 'my-orders':
        return renderOrdersView();
      case 'profile':
        return renderProfileView();
      default:
        return renderBooksView();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          {activeView === 'my-orders' ? 'My Orders' : 
           activeView === 'profile' ? 'My Profile' : 'Browse Books'}
        </h2>
        <p className="text-muted-foreground">
          {activeView === 'my-orders' ? 'View your order history' : 
           activeView === 'profile' ? 'Manage your account information' : 'Discover and purchase books'}
        </p>
      </div>

      {renderContent()}

      {/* Shopping Cart Sidebar */}
      <CartSidebar isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
    </div>
  );
}
