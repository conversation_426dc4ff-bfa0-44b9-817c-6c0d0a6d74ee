import { useState, useEffect } from 'react';
import { Book, Users, ShoppingCart, DollarSign, Edit, Trash2, Ban } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Chart } from '../components/Chart';
import { getAllBooks, deleteBook } from '../services/bookService';
import { getAllUsers, updateUser as updateUserService, blockUser, deleteUser } from '../services/userService';
import { getAllOrders } from '../services/orderService';
import { Book as BookType, User, Order } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { markCriterionComplete } from '../lib/criteria';

interface AdminDashboardProps {
  activeView?: string;
}

export function AdminDashboard({ activeView = 'dashboard' }: AdminDashboardProps) {
  const { toast } = useToast();
  const [stats, setStats] = useState({
    totalBooks: 0,
    activeUsers: 0,
    totalOrders: 0,
    revenue: 0,
  });
  const [books, setBooks] = useState<BookType[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [chartData, setChartData] = useState({
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [{
      label: 'Monthly Sales',
      data: [1200, 1900, 3000, 2500, 2200, 3200],
      borderColor: 'hsl(221 83% 53%)',
      backgroundColor: 'hsla(221 83% 53% / 0.1)',
      tension: 0.4,
      fill: true,
    }],
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    // Criteria 7: Nested loops (users and their orders)
    markCriterionComplete(7, "Nested Loops");

    const allBooks = getAllBooks();
    const allUsers = getAllUsers();
    const allOrders = getAllOrders();

    setBooks(allBooks);
    setUsers(allUsers);
    setOrders(allOrders);

    // Calculate stats
    const activeUsers = allUsers.filter(user => !user.blocked).length;
    const totalRevenue = allOrders.reduce((sum, order) => sum + order.total, 0);

    setStats({
      totalBooks: allBooks.length,
      activeUsers,
      totalOrders: allOrders.length,
      revenue: totalRevenue,
    });

    // Generate chart data based on orders
    generateChartData(allOrders);
  };

  const generateChartData = (orders: Order[]) => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthlySales = new Array(12).fill(0);

    orders.forEach(order => {
      const date = new Date(order.createdAt);
      const month = date.getMonth();
      monthlySales[month] += order.total;
    });

    setChartData({
      labels: monthNames,
      datasets: [{
        label: 'Monthly Sales',
        data: monthlySales,
        borderColor: 'hsl(221 83% 53%)',
        backgroundColor: 'hsla(221 83% 53% / 0.1)',
        tension: 0.4,
        fill: true,
      }],
    });
  };

  const handleBlockUser = (userId: string) => {
    const success = blockUser(userId);
    if (success) {
      toast({ title: "User blocked successfully" });
      loadData();
    }
  };

  const handleDeleteUser = (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      const success = deleteUser(userId);
      if (success) {
        toast({ title: "User deleted successfully" });
        loadData();
      }
    }
  };

  const handleDeleteBook = (bookId: string) => {
    if (window.confirm('Are you sure you want to delete this book?')) {
      const success = deleteBook(bookId);
      if (success) {
        toast({ title: "Book deleted successfully" });
        loadData();
      }
    }
  };

  const recentOrders = orders.slice(-5).reverse();
  const recentUsers = users.slice(-5).reverse();

  const renderDashboardView = () => (
    <>
      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Book className="h-6 w-6 text-primary" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">Total Books</p>
                <p className="text-2xl font-bold" data-testid="stat-total-books">{stats.totalBooks}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold" data-testid="stat-active-users">{stats.activeUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <ShoppingCart className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold" data-testid="stat-total-orders">{stats.totalOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">Revenue</p>
                <p className="text-2xl font-bold" data-testid="stat-revenue">${stats.revenue.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sales Chart */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Sales Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <Chart data={chartData} type="line" />
        </CardContent>
      </Card>

      {/* Recent Orders and Users */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
          </CardHeader>
          <CardContent>
            {recentOrders.length === 0 ? (
              <div className="text-center text-muted-foreground py-4" data-testid="text-no-orders">
                No orders yet
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentOrders.map((order) => {
                      const customer = users.find(u => u.id === order.buyerId);
                      return (
                        <TableRow key={order.id} data-testid={`order-row-${order.id}`}>
                          <TableCell className="text-primary font-mono text-xs" data-testid={`text-order-id-${order.id}`}>
                            #{order.id.slice(0, 8)}
                          </TableCell>
                          <TableCell data-testid={`text-order-customer-${order.id}`}>
                            {customer?.name || 'Unknown'}
                          </TableCell>
                          <TableCell data-testid={`text-order-total-${order.id}`}>
                            ${order.total.toFixed(2)}
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">Completed</Badge>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
          </CardHeader>
          <CardContent>
            {users.length === 0 ? (
              <div className="text-center text-muted-foreground py-4" data-testid="text-no-users">
                No users yet
              </div>
            ) : (
              <div className="space-y-3">
                {recentUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 bg-muted rounded-lg"
                    data-testid={`user-row-${user.id}`}
                  >
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">
                        {user.name.charAt(0).toUpperCase()}
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium" data-testid={`text-user-name-${user.id}`}>
                          {user.name}
                        </p>
                        <p className="text-xs text-muted-foreground" data-testid={`text-user-details-${user.id}`}>
                          {user.role.charAt(0).toUpperCase() + user.role.slice(1)} • {user.email}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      {!user.blocked && user.role !== 'admin' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleBlockUser(user.id)}
                          data-testid={`button-block-user-${user.id}`}
                        >
                          <Ban className="h-3 w-3" />
                        </Button>
                      )}
                      {user.role !== 'admin' && (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteUser(user.id)}
                          data-testid={`button-delete-user-${user.id}`}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );

  const renderBooksView = () => (
    <Card>
      <CardHeader>
        <CardTitle>All Books Management</CardTitle>
      </CardHeader>
      <CardContent>
        {books.length === 0 ? (
          <div className="text-center text-muted-foreground py-4" data-testid="text-no-books">
            No books available
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Author</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Seller</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {books.map((book) => {
                  const seller = users.find(u => u.id === book.sellerId);
                  return (
                    <TableRow key={book.id} data-testid={`book-row-${book.id}`}>
                      <TableCell className="font-medium" data-testid={`text-book-title-${book.id}`}>
                        {book.title}
                      </TableCell>
                      <TableCell data-testid={`text-book-author-${book.id}`}>{book.author}</TableCell>
                      <TableCell data-testid={`text-book-price-${book.id}`}>${book.price.toFixed(2)}</TableCell>
                      <TableCell>
                        <Badge variant={book.stock > 0 ? "secondary" : "destructive"} data-testid={`text-book-stock-${book.id}`}>
                          {book.stock}
                        </Badge>
                      </TableCell>
                      <TableCell data-testid={`text-book-seller-${book.id}`}>
                        {seller?.name || 'Unknown'}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteBook(book.id)}
                          data-testid={`button-delete-book-${book.id}`}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderUsersView = () => (
    <Card>
      <CardHeader>
        <CardTitle>All Users Management</CardTitle>
      </CardHeader>
      <CardContent>
        {users.length === 0 ? (
          <div className="text-center text-muted-foreground py-4" data-testid="text-no-users">
            No users yet
          </div>
        ) : (
          <div className="space-y-3">
            {users.map((user) => (
              <div
                key={user.id}
                className="flex items-center justify-between p-4 bg-muted rounded-lg"
                data-testid={`user-row-${user.id}`}
              >
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium" data-testid={`text-user-name-${user.id}`}>
                      {user.name}
                    </p>
                    <p className="text-xs text-muted-foreground" data-testid={`text-user-details-${user.id}`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)} • {user.email}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {user.blocked ? 'Blocked' : 'Active'} • Joined {new Date(user.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  {!user.blocked && user.role !== 'admin' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleBlockUser(user.id)}
                      data-testid={`button-block-user-${user.id}`}
                    >
                      <Ban className="h-3 w-3" />
                    </Button>
                  )}
                  {user.role !== 'admin' && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteUser(user.id)}
                      data-testid={`button-delete-user-${user.id}`}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderOrdersView = () => (
    <Card>
      <CardHeader>
        <CardTitle>All Orders Management</CardTitle>
      </CardHeader>
      <CardContent>
        {orders.length === 0 ? (
          <div className="text-center text-muted-foreground py-4" data-testid="text-no-orders">
            No orders yet
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orders.map((order) => {
                  const customer = users.find(u => u.id === order.buyerId);
                  return (
                    <TableRow key={order.id} data-testid={`order-row-${order.id}`}>
                      <TableCell className="text-primary font-mono text-xs" data-testid={`text-order-id-${order.id}`}>
                        #{order.id.slice(0, 8)}
                      </TableCell>
                      <TableCell data-testid={`text-order-customer-${order.id}`}>
                        {customer?.name || 'Unknown'}
                      </TableCell>
                      <TableCell data-testid={`text-order-items-${order.id}`}>
                        {order.items.reduce((total, item) => total + item.qty, 0)} items
                      </TableCell>
                      <TableCell data-testid={`text-order-total-${order.id}`}>
                        ${order.total.toFixed(2)}
                      </TableCell>
                      <TableCell data-testid={`text-order-date-${order.id}`}>
                        {new Date(order.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">Completed</Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderAnalyticsView = () => (
    <>
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Book className="h-6 w-6 text-primary" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">Total Books</p>
                <p className="text-2xl font-bold" data-testid="stat-total-books">{stats.totalBooks}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold" data-testid="stat-active-users">{stats.activeUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <ShoppingCart className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold" data-testid="stat-total-orders">{stats.totalOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">Revenue</p>
                <p className="text-2xl font-bold" data-testid="stat-revenue">${stats.revenue.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sales Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Sales Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <Chart data={chartData} type="line" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Sales by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <Chart 
              data={{
                labels: ['Fiction', 'Non-Fiction', 'Science', 'History', 'Biography'],
                datasets: [{
                  label: 'Books Sold',
                  data: [35, 25, 15, 20, 5],
                  backgroundColor: '#3b82f6',
                }]
              }} 
              type="doughnut" 
            />
          </CardContent>
        </Card>
      </div>
    </>
  );

  const renderContent = () => {
    switch (activeView) {
      case 'books':
        return renderBooksView();
      case 'users':
        return renderUsersView();
      case 'orders':
        return renderOrdersView();
      case 'analytics':
        return renderAnalyticsView();
      default:
        return renderDashboardView();
    }
  };

  return (
    <div className="p-6">
      {/* Dashboard Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          {activeView === 'books' ? 'All Books' : 
           activeView === 'users' ? 'User Management' :
           activeView === 'orders' ? 'Order Management' :
           activeView === 'analytics' ? 'Analytics' : 'Admin Dashboard'}
        </h2>
        <p className="text-muted-foreground">
          {activeView === 'books' ? 'Manage all books in the store' : 
           activeView === 'users' ? 'Manage user accounts and permissions' :
           activeView === 'orders' ? 'View and manage all orders' :
           activeView === 'analytics' ? 'Sales analytics and reports' : 'Manage your bookstore operations'}
        </p>
      </div>

      {renderContent()}
    </div>
  );
}
