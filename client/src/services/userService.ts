import { User, InsertUser } from "@shared/schema";
import { LocalStorage } from "../lib/storage";
import { generateUUID } from "../lib/uuid";
import { markCriterionComplete } from "../lib/criteria";

export function registerUser(userData: Omit<InsertUser, 'passwordHash'> & { password: string }): User {
  // Criteria 3: Objects as data records
  markCriterionComplete(3, "Objects as Data Records");
  
  // Criteria 13: File I/O via localStorage
  markCriterionComplete(13, "File I/O");

  const users = LocalStorage.getUsers();
  
  // Check if email already exists
  const existingUser = users.find(u => u.email === userData.email);
  if (existingUser) {
    throw new Error('Email already exists');
  }

  const user: User = {
    id: generateUUID(),
    name: userData.name,
    email: userData.email,
    passwordHash: userData.password, // In real app, this would be hashed
    role: userData.role,
    blocked: false,
    createdAt: Date.now(),
  };

  users.push(user);
  LocalStorage.saveUsers(users);
  
  return user;
}

export function loginUser(email: string, password: string): User | null {
  // Criteria 15: Use of sentinels or flags
  markCriterionComplete(15, "Sentinels or Flags");

  const users = LocalStorage.getUsers();
  const user = users.find(u => u.email === email && u.passwordHash === password);
  
  if (user && !user.blocked) {
    LocalStorage.saveCurrentUser(user.id);
    return user;
  }
  
  return null;
}

export function getCurrentUser(): User | null {
  const currentUserId = LocalStorage.getCurrentUser();
  if (!currentUserId) return null;
  
  const users = LocalStorage.getUsers();
  return users.find(u => u.id === currentUserId) || null;
}

export function getAllUsers(): User[] {
  // Criteria 1: Arrays
  markCriterionComplete(1, "Arrays");
  
  return LocalStorage.getUsers();
}

export function updateUser(userId: string, updates: Partial<User>): User | null {
  const users = LocalStorage.getUsers();
  const userIndex = users.findIndex(u => u.id === userId);
  
  if (userIndex === -1) return null;
  
  const updatedUser = { ...users[userIndex], ...updates };
  users[userIndex] = updatedUser;
  LocalStorage.saveUsers(users);
  
  return updatedUser;
}

export function blockUser(userId: string): boolean {
  const user = updateUser(userId, { blocked: true });
  return user !== null;
}

export function deleteUser(userId: string): boolean {
  const users = LocalStorage.getUsers();
  const filteredUsers = users.filter(u => u.id !== userId);
  
  if (filteredUsers.length < users.length) {
    LocalStorage.saveUsers(filteredUsers);
    return true;
  }
  
  return false;
}

export function logout(): void {
  LocalStorage.saveCurrentUser(null);
}
