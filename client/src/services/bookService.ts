import { Book, InsertBook } from "@shared/schema";
import { LocalStorage } from "../lib/storage";
import { generateUUID } from "../lib/uuid";
import { markCriterionComplete } from "../lib/criteria";

export function addBook(bookData: InsertBook): Book {
  // Criteria 2: User-defined objects
  markCriterionComplete(2, "User-defined Objects");
  
  // Criteria 8: User-defined methods
  markCriterionComplete(8, "User-defined Methods");

  const books = LocalStorage.getBooks();
  
  const book: Book = {
    id: generateUUID(),
    ...bookData,
    createdAt: Date.now(),
  };

  books.push(book);
  LocalStorage.saveBooks(books);
  
  return book;
}

export function updateBook(bookId: string, updates: Partial<Book>): Book | null {
  // Criteria 8: User-defined methods
  markCriterionComplete(8, "User-defined Methods");

  const books = LocalStorage.getBooks();
  const bookIndex = books.findIndex(b => b.id === bookId);
  
  if (bookIndex === -1) return null;
  
  const updatedBook = { ...books[bookIndex], ...updates };
  books[bookIndex] = updatedBook;
  LocalStorage.saveBooks(books);
  
  return updatedBook;
}

export function deleteBook(bookId: string): boolean {
  // Criteria 8: User-defined methods
  markCriterionComplete(8, "User-defined Methods");

  const books = LocalStorage.getBooks();
  const filteredBooks = books.filter(b => b.id !== bookId);
  
  if (filteredBooks.length < books.length) {
    LocalStorage.saveBooks(filteredBooks);
    return true;
  }
  
  return false;
}

export function getAllBooks(): Book[] {
  // Criteria 1: Arrays
  markCriterionComplete(1, "Arrays");
  
  // Criteria 6: Loops (when books are rendered)
  markCriterionComplete(6, "Loops");

  return LocalStorage.getBooks();
}

export function searchBooksByTitle(title: string): Book[] {
  // Criteria 9: User-defined methods with parameters
  markCriterionComplete(9, "Methods with Parameters");
  
  // Criteria 12: Searching
  markCriterionComplete(12, "Searching");

  const books = LocalStorage.getBooks();
  return books.filter(book => 
    book.title.toLowerCase().includes(title.toLowerCase()) ||
    book.author.toLowerCase().includes(title.toLowerCase())
  );
}

export function sortBooks(by: 'price' | 'title', order: 'asc' | 'desc' = 'asc'): Book[] {
  // Criteria 11: Sorting
  markCriterionComplete(11, "Sorting");

  const books = LocalStorage.getBooks();
  
  return [...books].sort((a, b) => {
    let comparison = 0;
    
    if (by === 'price') {
      comparison = a.price - b.price;
    } else if (by === 'title') {
      comparison = a.title.localeCompare(b.title);
    }
    
    return order === 'desc' ? -comparison : comparison;
  });
}

export function getBooksBySeller(sellerId: string): Book[] {
  const books = LocalStorage.getBooks();
  return books.filter(book => book.sellerId === sellerId);
}

export function getBook(bookId: string): Book | null {
  const books = LocalStorage.getBooks();
  return books.find(book => book.id === bookId) || null;
}
