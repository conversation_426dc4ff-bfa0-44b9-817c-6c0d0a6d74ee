import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Cart } from '@shared/schema';
import { getCart, addToCart, updateCartItemQuantity, removeFromCart, clearCart } from '../services/orderService';

interface CartContextType {
  cart: Cart;
  addItem: (bookId: string, quantity: number) => void;
  updateQuantity: (bookId: string, quantity: number) => void;
  removeItem: (bookId: string) => void;
  clear: () => void;
  itemCount: number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const [cart, setCart] = useState<Cart>({ items: [], total: 0 });

  useEffect(() => {
    const currentCart = getCart();
    setCart(currentCart);
  }, []);

  const addItem = (bookId: string, quantity: number) => {
    const userId = 'current-user'; // This would come from auth context
    const updatedCart = addToCart(userId, bookId, quantity);
    setCart(updatedCart);
  };

  const updateQuantity = (bookId: string, quantity: number) => {
    const updatedCart = updateCartItemQuantity(bookId, quantity);
    setCart(updatedCart);
  };

  const removeItem = (bookId: string) => {
    const updatedCart = removeFromCart(bookId);
    setCart(updatedCart);
  };

  const clear = () => {
    clearCart();
    setCart({ items: [], total: 0 });
  };

  const itemCount = cart.items.reduce((count, item) => count + item.quantity, 0);

  return (
    <CartContext.Provider value={{ cart, addItem, updateQuantity, removeItem, clear, itemCount }}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
