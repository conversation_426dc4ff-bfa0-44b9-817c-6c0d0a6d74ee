import { User, Book, Order } from "@shared/schema";
import { LocalStorage } from "./storage";
import { generateUUID } from "./uuid";

export function seedData() {
  // Create admin user
  const adminId = generateUUID();
  const admin: User = {
    id: adminId,
    name: "Admin User",
    email: "<EMAIL>",
    passwordHash: "Admin@123", // In real app, this would be hashed
    role: "admin",
    blocked: false,
    createdAt: Date.now(),
  };

  // Create sample sellers
  const seller1Id = generateUUID();
  const seller1: User = {
    id: seller1Id,
    name: "<PERSON>",
    email: "<EMAIL>",
    passwordHash: "password123",
    role: "seller",
    blocked: false,
    createdAt: Date.now(),
  };

  const seller2Id = generateUUID();
  const seller2: User = {
    id: seller2Id,
    name: "<PERSON>",
    email: "<EMAIL>",
    passwordHash: "password123",
    role: "seller",
    blocked: false,
    createdAt: Date.now(),
  };

  // Create sample buyers
  const buyer1Id = generateUUID();
  const buyer1: User = {
    id: buyer1Id,
    name: "<PERSON>",
    email: "<EMAIL>",
    passwordHash: "password123",
    role: "buyer",
    blocked: false,
    createdAt: Date.now(),
  };

  const buyer2Id = generateUUID();
  const buyer2: User = {
    id: buyer2Id,
    name: "Bob Wilson",
    email: "<EMAIL>",
    passwordHash: "password123",
    role: "buyer",
    blocked: false,
    createdAt: Date.now(),
  };

  const users = [admin, seller1, seller2, buyer1, buyer2];

  // Create sample books
  const books: Book[] = [
    {
      id: generateUUID(),
      title: "The Great Gatsby",
      author: "F. Scott Fitzgerald",
      price: 14.99,
      stock: 12,
      sellerId: seller1Id,
      imageBase64: "",
      createdAt: Date.now(),
    },
    {
      id: generateUUID(),
      title: "To Kill a Mockingbird",
      author: "Harper Lee",
      price: 12.99,
      stock: 8,
      sellerId: seller1Id,
      imageBase64: "",
      createdAt: Date.now(),
    },
    {
      id: generateUUID(),
      title: "1984",
      author: "George Orwell",
      price: 13.99,
      stock: 15,
      sellerId: seller1Id,
      imageBase64: "",
      createdAt: Date.now(),
    },
    {
      id: generateUUID(),
      title: "Pride and Prejudice",
      author: "Jane Austen",
      price: 11.99,
      stock: 10,
      sellerId: seller2Id,
      imageBase64: "",
      createdAt: Date.now(),
    },
    {
      id: generateUUID(),
      title: "The Catcher in the Rye",
      author: "J.D. Salinger",
      price: 15.99,
      stock: 6,
      sellerId: seller2Id,
      imageBase64: "",
      createdAt: Date.now(),
    },
  ];

  // Create sample orders
  const order1Id = generateUUID();
  const order1: Order = {
    id: order1Id,
    buyerId: buyer1Id,
    items: [
      { bookId: books[0].id, qty: 2, priceAtPurchase: 14.99 },
      { bookId: books[1].id, qty: 1, priceAtPurchase: 12.99 },
    ],
    total: 42.97,
    createdAt: Date.now() - 86400000, // 1 day ago
  };

  const order2Id = generateUUID();
  const order2: Order = {
    id: order2Id,
    buyerId: buyer2Id,
    items: [
      { bookId: books[2].id, qty: 1, priceAtPurchase: 13.99 },
    ],
    total: 13.99,
    createdAt: Date.now() - 43200000, // 12 hours ago
  };

  const orders = [order1, order2];

  // Save all data to localStorage
  LocalStorage.saveUsers(users);
  LocalStorage.saveBooks(books);
  LocalStorage.saveOrders(orders);

  // Initialize empty cart and criteria flags
  LocalStorage.saveCart({ items: [], total: 0 });
  LocalStorage.saveCriteriaFlags({
    criterion1: false, criterion2: false, criterion3: false, criterion4: false, criterion5: false,
    criterion6: false, criterion7: false, criterion8: false, criterion9: false, criterion10: false,
    criterion11: false, criterion12: false, criterion13: false, criterion14: false, criterion15: false,
  });

  console.log('BookStore demo data seeded successfully!');
}
