import { LocalStorage } from './storage';
import { useToast } from '@/hooks/use-toast';

const CRITERIA_DESCRIPTIONS = {
  criterion1: 'Arrays',
  criterion2: 'User-defined Objects',
  criterion3: 'Objects as Data Records',
  criterion4: 'Simple Selection',
  criterion5: 'Complex Selection',
  criterion6: 'Loops',
  criterion7: 'Nested Loops',
  criterion8: 'User-defined Methods',
  criterion9: 'Methods with Parameters',
  criterion10: 'Methods with Return Values',
  criterion11: 'Sorting',
  criterion12: 'Searching',
  criterion13: 'File I/O',
  criterion14: 'Additional Libraries',
  criterion15: 'Sentinels or Flags',
} as const;

export function markCriterionComplete(criterionNumber: number, taskName?: string) {
  const criterionKey = `criterion${criterionNumber}` as keyof typeof CRITERIA_DESCRIPTIONS;
  const flags = LocalStorage.getCriteriaFlags();
  
  if (!flags[criterionKey]) {
    flags[criterionKey] = true;
    LocalStorage.saveCriteriaFlags(flags);
    
    const description = CRITERIA_DESCRIPTIONS[criterionKey];
    const displayName = taskName || description;
    
    // Show toast notification
    showCriteriaToast(criterionNumber, displayName);
    
    // Check if all criteria are complete
    const allComplete = Object.values(flags).every(Boolean);
    if (allComplete) {
      showFinalToast();
    }
  }
}

function showCriteriaToast(criterionNumber: number, taskName: string) {
  // Create a toast element since we can't use useToast outside of components
  const container = document.getElementById('toast-container') || createToastContainer();
  
  const toast = document.createElement('div');
  toast.className = 'toast';
  toast.innerHTML = `
    <i class="fas fa-check-circle"></i>
    <span>✅ Criteria ${criterionNumber} – ${taskName} – Complete</span>
  `;
  
  container.appendChild(toast);
  setTimeout(() => toast.classList.add('show'), 100);
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => container.removeChild(toast), 300);
  }, 4000);
}

function showFinalToast() {
  const container = document.getElementById('toast-container') || createToastContainer();
  
  const toast = document.createElement('div');
  toast.className = 'toast';
  toast.style.background = '#8B5CF6';
  toast.innerHTML = `
    <i class="fas fa-trophy"></i>
    <span>🎉 All 15 Criteria Completed Successfully!</span>
  `;
  
  container.appendChild(toast);
  setTimeout(() => toast.classList.add('show'), 100);
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => container.removeChild(toast), 300);
  }, 6000);
}

function createToastContainer() {
  const container = document.createElement('div');
  container.id = 'toast-container';
  container.className = 'toast-container';
  document.body.appendChild(container);
  return container;
}

export function getCriteriaProgress() {
  const flags = LocalStorage.getCriteriaFlags();
  const completed = Object.values(flags).filter(Boolean).length;
  return { completed, total: 15, flags };
}
