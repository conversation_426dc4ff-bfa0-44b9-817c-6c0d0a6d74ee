import { useState } from 'react';
import { X, Minus, Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { getBook } from '../services/bookService';
import { checkout } from '../services/orderService';
import { useToast } from '@/hooks/use-toast';

interface CartSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CartSidebar({ isOpen, onClose }: CartSidebarProps) {
  const { cart, updateQuantity, removeItem, clear } = useCart();
  const { user } = useAuth();
  const { toast } = useToast();
  const [isCheckingOut, setIsCheckingOut] = useState(false);

  const getCartItemsWithBooks = () => {
    return cart.items.map(item => {
      const book = getBook(item.bookId);
      return { ...item, book };
    }).filter(item => item.book);
  };

  const handleCheckout = async () => {
    if (!user || user.role !== 'buyer') return;
    
    setIsCheckingOut(true);
    try {
      const order = checkout(user.id, cart);
      toast({
        title: "Order Placed Successfully!",
        description: `Order #${order.id.slice(0, 8)} for $${order.total.toFixed(2)}`,
      });
      clear();
      onClose();
    } catch (error) {
      toast({
        title: "Checkout Failed",
        description: error instanceof Error ? error.message : "Unable to process order",
        variant: "destructive",
      });
    } finally {
      setIsCheckingOut(false);
    }
  };

  const cartItems = getCartItemsWithBooks();

  return (
    <div
      className={`fixed inset-y-0 right-0 w-80 bg-card border-l border-border transform transition-transform z-50 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}
      data-testid="cart-sidebar"
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-border">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold" data-testid="text-cart-title">Shopping Cart</h3>
            <Button variant="ghost" size="icon" onClick={onClose} data-testid="button-close-cart">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto p-4">
          {cartItems.length === 0 ? (
            <div className="text-center text-muted-foreground py-8" data-testid="text-cart-empty">
              Your cart is empty
            </div>
          ) : (
            <div className="space-y-4">
              {cartItems.map((item) => (
                <div
                  key={item.bookId}
                  className="flex items-center space-x-3 p-3 bg-muted rounded-lg"
                  data-testid={`cart-item-${item.bookId}`}
                >
                  <img
                    src={item.book?.imageBase64 || `https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&h=80&q=80`}
                    alt={item.book?.title}
                    className="w-12 h-16 object-cover rounded"
                    data-testid={`img-cart-item-${item.bookId}`}
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-sm" data-testid={`text-cart-item-title-${item.bookId}`}>
                      {item.book?.title}
                    </h4>
                    <p className="text-xs text-muted-foreground" data-testid={`text-cart-item-price-${item.bookId}`}>
                      ${item.book?.price.toFixed(2)}
                    </p>
                    <div className="flex items-center mt-1 space-x-2">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => updateQuantity(item.bookId, item.quantity - 1)}
                        data-testid={`button-decrease-quantity-${item.bookId}`}
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                      <Badge variant="secondary" data-testid={`text-cart-item-quantity-${item.bookId}`}>
                        {item.quantity}
                      </Badge>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => updateQuantity(item.bookId, item.quantity + 1)}
                        data-testid={`button-increase-quantity-${item.bookId}`}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-destructive"
                        onClick={() => removeItem(item.bookId)}
                        data-testid={`button-remove-item-${item.bookId}`}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        {cartItems.length > 0 && (
          <div className="p-4 border-t border-border">
            <div className="flex justify-between items-center mb-4">
              <span className="font-semibold">Total:</span>
              <span className="font-bold text-lg" data-testid="text-cart-total">
                ${cart.total.toFixed(2)}
              </span>
            </div>
            <Button
              className="w-full"
              onClick={handleCheckout}
              disabled={isCheckingOut || cartItems.length === 0}
              data-testid="button-checkout"
            >
              {isCheckingOut ? 'Processing...' : 'Proceed to Checkout'}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
