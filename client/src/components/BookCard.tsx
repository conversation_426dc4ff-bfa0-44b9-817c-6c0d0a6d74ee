import { Book } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';

interface BookCardProps {
  book: Book;
  onEdit?: (book: Book) => void;
  onDelete?: (bookId: string) => void;
  showActions?: boolean;
}

export function BookCard({ book, onEdit, onDelete, showActions = false }: BookCardProps) {
  const { addItem } = useCart();
  const { user } = useAuth();

  const handleAddToCart = () => {
    if (user?.role === 'buyer') {
      addItem(book.id, 1);
    }
  };

  const getImageSrc = () => {
    if (book.imageBase64) {
      return book.imageBase64;
    }
    // Fallback placeholder image
    return `https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=400&q=80`;
  };

  return (
    <Card className="hover:shadow-lg transition-shadow" data-testid={`card-book-${book.id}`}>
      <CardContent className="p-4">
        <img
          src={getImageSrc()}
          alt={`${book.title} cover`}
          className="w-full h-48 object-cover rounded-md mb-4"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = `https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=400&q=80`;
          }}
          data-testid={`img-book-cover-${book.id}`}
        />
        
        <h3 className="font-semibold text-lg mb-2" data-testid={`text-book-title-${book.id}`}>
          {book.title}
        </h3>
        
        <p className="text-muted-foreground text-sm mb-2" data-testid={`text-book-author-${book.id}`}>
          {book.author}
        </p>
        
        <div className="flex justify-between items-center mb-3">
          <span className="text-xl font-bold text-primary" data-testid={`text-book-price-${book.id}`}>
            ${book.price.toFixed(2)}
          </span>
          <Badge variant={book.stock > 0 ? "secondary" : "destructive"} data-testid={`badge-book-stock-${book.id}`}>
            {book.stock > 0 ? `${book.stock} in stock` : 'Out of stock'}
          </Badge>
        </div>

        {showActions ? (
          <div className="flex space-x-2">
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(book)}
                data-testid={`button-edit-book-${book.id}`}
              >
                Edit
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => onDelete(book.id)}
                data-testid={`button-delete-book-${book.id}`}
              >
                Delete
              </Button>
            )}
          </div>
        ) : user?.role === 'buyer' && book.stock > 0 ? (
          <Button
            className="w-full"
            onClick={handleAddToCart}
            disabled={book.stock === 0}
            data-testid={`button-add-to-cart-${book.id}`}
          >
            Add to Cart
          </Button>
        ) : null}
      </CardContent>
    </Card>
  );
}
