import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider, useAuth } from './context/AuthContext';
import { CartProvider } from './context/CartContext';
import { AuthForm } from './components/AuthForm';
import { Dashboard } from './pages/Dashboard';
import { useEffect } from 'react';
import { LocalStorage } from './lib/storage';
import { seedData } from './lib/seedData';
import NotFound from "@/pages/not-found";

function AppContent() {
  const { isLoggedIn } = useAuth();

  useEffect(() => {
    // Initialize localStorage with seed data if empty
    const users = LocalStorage.getUsers();
    if (users.length === 0) {
      seedData();
    }
  }, []);

  return (
    <Switch>
      <Route path="/">
        {isLoggedIn ? <Dashboard /> : <AuthForm />}
      </Route>
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <AuthProvider>
          <CartProvider>
            <Toaster />
            <AppContent />
          </CartProvider>
        </AuthProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
